{"expo": {"name": "EduNova Parent & Student", "slug": "edu-sis-parent-student", "version": "1.0.0", "orientation": "default", "icon": "./assets/parent_student_app_logo.png", "darkIcon": "./assets/parent_student_app_logo_dark.png", "userInterfaceStyle": "light", "newArchEnabled": true, "plugins": [["@react-native-firebase/app", {"ios": {"googleServicesFile": "./GoogleService-Info.plist"}, "android": {"googleServicesFile": "./google-services.json"}}], "@react-native-firebase/messaging", ["expo-notifications", {"color": "#007AFF"}], ["expo-build-properties", {"ios": {"useFrameworks": "static"}, "android": {"compileSdkVersion": 35, "targetSdkVersion": 35, "minSdkVersion": 24, "buildToolsVersion": "35.0.0"}}]], "splash": {"image": "./assets/parent_student_splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff", "dark": {"image": "./assets/parent_student_splash_dark.png", "resizeMode": "contain", "backgroundColor": "#000000"}}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.edunovaasia.parentstudent", "buildNumber": "1", "runtimeVersion": "1.0.0", "googleServicesFile": "./GoogleService-Info.plist", "config": {"usesNonExemptEncryption": false}, "infoPlist": {"UIBackgroundModes": ["remote-notification"], "UISupportedInterfaceOrientations": ["UIInterfaceOrientationPortrait"], "UISupportedInterfaceOrientations~ipad": ["UIInterfaceOrientationPortrait", "UIInterfaceOrientationPortraitUpsideDown", "UIInterfaceOrientationLandscapeLeft", "UIInterfaceOrientationLandscapeRight"], "ITSAppUsesNonExemptEncryption": false}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/parent_student_app_logo.png", "backgroundColor": "#ffffff", "darkForegroundImage": "./assets/parent_student_app_logo_dark.png", "darkBackgroundColor": "#000000"}, "package": "com.edunovaasia.parentstudent", "runtimeVersion": "1.0.0", "googleServicesFile": "./google-services.json", "screenOrientation": "portrait", "compileSdkVersion": 35, "targetSdkVersion": 35, "minSdkVersion": 24, "versionCode": 1}, "web": {"favicon": "./assets/parent_student_app_logo.png"}, "extra": {"eas": {"projectId": "parent-student-app-project-id"}}, "updates": {"url": "https://u.expo.dev/parent-student-app-project-id"}}}