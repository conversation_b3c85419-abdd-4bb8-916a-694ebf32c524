import React, { useEffect, useRef, useState } from 'react';
import { StatusBar } from 'expo-status-bar';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { StyleSheet } from 'react-native';

// Import shared components and contexts
import {
  ThemeProvider,
  LanguageProvider,
  NotificationProvider,
  MessagingProvider,
  requestUserPermission,
  notificationListener,
  setupLocalNotifications,
  setNavigationRef,
} from 'edu-sis-shared';

// Import parent/student-specific screens
import HomeScreen from '../../shared/src/screens/HomeScreen';
import LoginScreen from '../../shared/src/screens/LoginScreen';
import ParentScreen from '../../shared/src/screens/ParentScreen';
import GradesScreen from '../../shared/src/screens/GradesScreen';
import AttendanceScreen from '../../shared/src/screens/AttendanceScreen';
import AssignmentsScreen from '../../shared/src/screens/AssignmentsScreen';
import AssignmentDetailScreen from '../../shared/src/screens/AssignmentDetailScreen';
import BehaviorScreen from '../../shared/src/screens/BehaviorScreen';
import TimetableScreen from '../../shared/src/screens/TimetableScreen';
import CalendarScreen from '../../shared/src/screens/CalendarScreen';
import UserCalendarScreen from '../../shared/src/screens/UserCalendarScreen';
import StudentMessagingScreen from '../../shared/src/screens/StudentMessagingScreen';
import StudentCreateConversationScreen from '../../shared/src/screens/StudentCreateConversationScreen';
import StudentHealthScreen from '../../shared/src/screens/StudentHealthScreen';
import CreateHealthRecordScreen from '../../shared/src/screens/CreateHealthRecordScreen';
import EditHealthInfoScreen from '../../shared/src/screens/EditHealthInfoScreen';
import StudentHomeworkDetailScreen from '../../shared/src/screens/StudentHomeworkDetailScreen';
import ConversationScreen from '../../shared/src/screens/ConversationScreen';
import NotificationScreen from '../../shared/src/screens/NotificationScreen';
import SettingsScreen from '../../shared/src/screens/SettingsScreen';
import WorkspaceScreen from '../../shared/src/screens/WorkspaceScreen';
import LibraryScreen from '../../shared/src/screens/LibraryScreen';
import ContactsScreen from '../../shared/src/screens/ContactsScreen';
import AboutUsScreen from '../../shared/src/screens/AboutUsScreen';
import FAQScreen from '../../shared/src/screens/FAQScreen';
import StudentReportsScreen from '../../shared/src/screens/StudentReportsScreen';
import ReportDetailScreen from '../../shared/src/screens/ReportDetailScreen';

const Stack = createNativeStackNavigator();

export default function App() {
  const navigationRef = useRef();
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Initialize messaging and notifications
        await requestUserPermission();
        await setupLocalNotifications();
        
        // Set up notification listeners
        const unsubscribe = notificationListener();
        
        setIsReady(true);
        
        return () => {
          if (unsubscribe) {
            unsubscribe();
          }
        };
      } catch (error) {
        console.error('Error initializing app:', error);
        setIsReady(true);
      }
    };

    initializeApp();
  }, []);

  if (!isReady) {
    return null; // Or a loading screen
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <SafeAreaProvider>
        <ThemeProvider>
          <LanguageProvider>
            <NotificationProvider>
              <MessagingProvider>
                <NavigationContainer
                  ref={navigationRef}
                  onReady={() => {
                    console.log('🧭 NAVIGATION: NavigationContainer is ready, setting reference...');
                    setTimeout(() => {
                      setNavigationRef(navigationRef.current);
                    }, 100);
                  }}
                >
                  <StatusBar style='auto' />
                  <Stack.Navigator
                    initialRouteName='Home'
                    screenOptions={{ headerShown: false }}
                  >
                    <Stack.Screen name='Home' component={HomeScreen} />
                    <Stack.Screen name='Login' component={LoginScreen} />
                    <Stack.Screen name='ParentScreen' component={ParentScreen} />
                    <Stack.Screen name='GradesScreen' component={GradesScreen} />
                    <Stack.Screen name='AttendanceScreen' component={AttendanceScreen} />
                    <Stack.Screen name='AssignmentsScreen' component={AssignmentsScreen} />
                    <Stack.Screen name='AssignmentDetail' component={AssignmentDetailScreen} />
                    <Stack.Screen name='BehaviorScreen' component={BehaviorScreen} />
                    <Stack.Screen name='TimetableScreen' component={TimetableScreen} />
                    <Stack.Screen name='CalendarScreen' component={CalendarScreen} />
                    <Stack.Screen name='UserCalendar' component={UserCalendarScreen} />
                    <Stack.Screen name='StudentMessagingScreen' component={StudentMessagingScreen} />
                    <Stack.Screen name='StudentCreateConversation' component={StudentCreateConversationScreen} />
                    <Stack.Screen name='StudentHealthScreen' component={StudentHealthScreen} />
                    <Stack.Screen name='CreateHealthRecord' component={CreateHealthRecordScreen} />
                    <Stack.Screen name='EditHealthInfo' component={EditHealthInfoScreen} />
                    <Stack.Screen name='StudentHomeworkDetail' component={StudentHomeworkDetailScreen} />
                    <Stack.Screen name='ConversationScreen' component={ConversationScreen} />
                    <Stack.Screen name='NotificationScreen' component={NotificationScreen} />
                    <Stack.Screen name='Settings' component={SettingsScreen} />
                    <Stack.Screen name='WorkspaceScreen' component={WorkspaceScreen} />
                    <Stack.Screen name='LibraryScreen' component={LibraryScreen} />
                    <Stack.Screen name='ContactsScreen' component={ContactsScreen} />
                    <Stack.Screen name='AboutUsScreen' component={AboutUsScreen} />
                    <Stack.Screen name='FAQScreen' component={FAQScreen} />
                    <Stack.Screen name='StudentReports' component={StudentReportsScreen} />
                    <Stack.Screen name='ReportDetail' component={ReportDetailScreen} />
                  </Stack.Navigator>
                </NavigationContainer>
              </MessagingProvider>
            </NotificationProvider>
          </LanguageProvider>
        </ThemeProvider>
      </SafeAreaProvider>
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  // Add any app-specific styles here
});
