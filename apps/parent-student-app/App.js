import React, { useEffect, useRef, useState } from 'react';
import { StatusBar } from 'expo-status-bar';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { SafeAreaProvider } from 'react-native-safe-area-context';
// Import local components and contexts
import { ThemeProvider } from './src/contexts/ThemeContext';
import { LanguageProvider } from './src/contexts/LanguageContext';
import { NotificationProvider } from './src/contexts/NotificationContext';
import { MessagingProvider } from './src/contexts/MessagingContext';
import {
  requestUserPermission,
  notificationListener,
  setupLocalNotifications,
  setNavigationRef,
} from './src/utils/messaging';

// Import parent/student-specific screens
import HomeScreen from './src/screens/HomeScreen';
import LoginScreen from './src/screens/LoginScreen';
import ParentScreen from './src/screens/ParentScreen';
import GradesScreen from './src/screens/GradesScreen';
import AttendanceScreen from './src/screens/AttendanceScreen';
import AssignmentsScreen from './src/screens/AssignmentsScreen';
import AssignmentDetailScreen from './src/screens/AssignmentDetailScreen';
import BehaviorScreen from './src/screens/BehaviorScreen';
import TimetableScreen from './src/screens/TimetableScreen';
import CalendarScreen from './src/screens/CalendarScreen';
import UserCalendarScreen from './src/screens/UserCalendarScreen';
import StudentMessagingScreen from './src/screens/StudentMessagingScreen';
import StudentCreateConversationScreen from './src/screens/StudentCreateConversationScreen';
import StudentHealthScreen from './src/screens/StudentHealthScreen';
import CreateHealthRecordScreen from './src/screens/CreateHealthRecordScreen';
import EditHealthInfoScreen from './src/screens/EditHealthInfoScreen';
import StudentHomeworkDetailScreen from './src/screens/StudentHomeworkDetailScreen';
import ConversationScreen from './src/screens/ConversationScreen';
import NotificationScreen from './src/screens/NotificationScreen';
import SettingsScreen from './src/screens/SettingsScreen';
import WorkspaceScreen from './src/screens/WorkspaceScreen';
import LibraryScreen from './src/screens/LibraryScreen';
import ContactsScreen from './src/screens/ContactsScreen';
import AboutUsScreen from './src/screens/AboutUsScreen';
import FAQScreen from './src/screens/FAQScreen';
import StudentReportsScreen from './src/screens/StudentReportsScreen';
import ReportDetailScreen from './src/screens/ReportDetailScreen';

const Stack = createNativeStackNavigator();

export default function App() {
  const navigationRef = useRef();
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Initialize messaging and notifications
        await requestUserPermission();
        await setupLocalNotifications();

        // Set up notification listeners
        const unsubscribe = notificationListener();

        setIsReady(true);

        return () => {
          if (unsubscribe) {
            unsubscribe();
          }
        };
      } catch (error) {
        console.error('Error initializing app:', error);
        setIsReady(true);
      }
    };

    initializeApp();
  }, []);

  if (!isReady) {
    return null; // Or a loading screen
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <SafeAreaProvider>
        <ThemeProvider>
          <LanguageProvider>
            <NotificationProvider>
              <MessagingProvider>
                <NavigationContainer
                  ref={navigationRef}
                  onReady={() => {
                    console.log(
                      '🧭 NAVIGATION: NavigationContainer is ready, setting reference...'
                    );
                    setTimeout(() => {
                      setNavigationRef(navigationRef.current);
                    }, 100);
                  }}
                >
                  <StatusBar style='auto' />
                  <Stack.Navigator
                    initialRouteName='Home'
                    screenOptions={{ headerShown: false }}
                  >
                    <Stack.Screen name='Home' component={HomeScreen} />
                    <Stack.Screen name='Login' component={LoginScreen} />
                    <Stack.Screen
                      name='ParentScreen'
                      component={ParentScreen}
                    />
                    <Stack.Screen
                      name='GradesScreen'
                      component={GradesScreen}
                    />
                    <Stack.Screen
                      name='AttendanceScreen'
                      component={AttendanceScreen}
                    />
                    <Stack.Screen
                      name='AssignmentsScreen'
                      component={AssignmentsScreen}
                    />
                    <Stack.Screen
                      name='AssignmentDetail'
                      component={AssignmentDetailScreen}
                    />
                    <Stack.Screen
                      name='BehaviorScreen'
                      component={BehaviorScreen}
                    />
                    <Stack.Screen
                      name='TimetableScreen'
                      component={TimetableScreen}
                    />
                    <Stack.Screen
                      name='CalendarScreen'
                      component={CalendarScreen}
                    />
                    <Stack.Screen
                      name='UserCalendar'
                      component={UserCalendarScreen}
                    />
                    <Stack.Screen
                      name='StudentMessagingScreen'
                      component={StudentMessagingScreen}
                    />
                    <Stack.Screen
                      name='StudentCreateConversation'
                      component={StudentCreateConversationScreen}
                    />
                    <Stack.Screen
                      name='StudentHealthScreen'
                      component={StudentHealthScreen}
                    />
                    <Stack.Screen
                      name='CreateHealthRecord'
                      component={CreateHealthRecordScreen}
                    />
                    <Stack.Screen
                      name='EditHealthInfo'
                      component={EditHealthInfoScreen}
                    />
                    <Stack.Screen
                      name='StudentHomeworkDetail'
                      component={StudentHomeworkDetailScreen}
                    />
                    <Stack.Screen
                      name='ConversationScreen'
                      component={ConversationScreen}
                    />
                    <Stack.Screen
                      name='NotificationScreen'
                      component={NotificationScreen}
                    />
                    <Stack.Screen name='Settings' component={SettingsScreen} />
                    <Stack.Screen
                      name='WorkspaceScreen'
                      component={WorkspaceScreen}
                    />
                    <Stack.Screen
                      name='LibraryScreen'
                      component={LibraryScreen}
                    />
                    <Stack.Screen
                      name='ContactsScreen'
                      component={ContactsScreen}
                    />
                    <Stack.Screen
                      name='AboutUsScreen'
                      component={AboutUsScreen}
                    />
                    <Stack.Screen name='FAQScreen' component={FAQScreen} />
                    <Stack.Screen
                      name='StudentReports'
                      component={StudentReportsScreen}
                    />
                    <Stack.Screen
                      name='ReportDetail'
                      component={ReportDetailScreen}
                    />
                  </Stack.Navigator>
                </NavigationContainer>
              </MessagingProvider>
            </NotificationProvider>
          </LanguageProvider>
        </ThemeProvider>
      </SafeAreaProvider>
    </GestureHandlerRootView>
  );
}
