import React, { useEffect, useRef, useState } from 'react';
import { StatusBar } from 'expo-status-bar';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { StyleSheet } from 'react-native';

// Import shared components and contexts
import {
  ThemeProvider,
  LanguageProvider,
  NotificationProvider,
  MessagingProvider,
  requestUserPermission,
  notificationListener,
  setupLocalNotifications,
  setNavigationRef,
} from 'edu-sis-shared';

// Import teacher-specific screens
import HomeScreen from '../../shared/src/screens/HomeScreen';
import LoginScreen from '../../shared/src/screens/LoginScreen';
import TeacherScreen from '../../shared/src/screens/TeacherScreen';
import TeacherProfile from '../../shared/src/screens/TeacherProfile';
import TeacherTimetable from '../../shared/src/screens/TeacherTimetable';
import TeacherAttendanceScreen from '../../shared/src/screens/TeacherAttendanceScreen';
import TeacherBPS from '../../shared/src/screens/TeacherBPS';
import TeacherHomeworkScreen from '../../shared/src/screens/TeacherHomeworkScreen';
import TeacherHomeworkDetailScreen from '../../shared/src/screens/TeacherHomeworkDetailScreen';
import TeacherHomeworkCreateScreen from '../../shared/src/screens/TeacherHomeworkCreateScreen';
import TeacherMessagingScreen from '../../shared/src/screens/TeacherMessagingScreen';
import TeacherHealthScreen from '../../shared/src/screens/TeacherHealthScreen';
import HomeroomScreen from '../../shared/src/screens/HomeroomScreen';
import HomeroomStudentsScreen from '../../shared/src/screens/HomeroomStudentsScreen';
import HomeroomStudentProfile from '../../shared/src/screens/HomeroomStudentProfile';
import HomeroomAttendanceDetailsScreen from '../../shared/src/screens/HomeroomAttendanceDetailsScreen';
import HomeroomDisciplineScreen from '../../shared/src/screens/HomeroomDisciplineScreen';
import ConversationScreen from '../../shared/src/screens/ConversationScreen';
import CreateConversationScreen from '../../shared/src/screens/CreateConversationScreen';
import NotificationScreen from '../../shared/src/screens/NotificationScreen';
import SettingsScreen from '../../shared/src/screens/SettingsScreen';
import WorkspaceScreen from '../../shared/src/screens/WorkspaceScreen';
import StaffReportsScreen from '../../shared/src/screens/StaffReportsScreen';
import ReportDetailScreen from '../../shared/src/screens/ReportDetailScreen';

const Stack = createNativeStackNavigator();

export default function App() {
  const navigationRef = useRef();
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Initialize messaging and notifications
        await requestUserPermission();
        await setupLocalNotifications();

        // Set up notification listeners
        const unsubscribe = notificationListener();

        setIsReady(true);

        return () => {
          if (unsubscribe) {
            unsubscribe();
          }
        };
      } catch (error) {
        console.error('Error initializing app:', error);
        setIsReady(true);
      }
    };

    initializeApp();
  }, []);

  if (!isReady) {
    return null; // Or a loading screen
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <SafeAreaProvider>
        <ThemeProvider>
          <LanguageProvider>
            <NotificationProvider>
              <MessagingProvider>
                <NavigationContainer
                  ref={navigationRef}
                  onReady={() => {
                    console.log(
                      '🧭 NAVIGATION: NavigationContainer is ready, setting reference...'
                    );
                    setTimeout(() => {
                      setNavigationRef(navigationRef.current);
                    }, 100);
                  }}
                >
                  <StatusBar style='auto' />
                  <Stack.Navigator
                    initialRouteName='Home'
                    screenOptions={{ headerShown: false }}
                  >
                    <Stack.Screen name='Home' component={HomeScreen} />
                    <Stack.Screen name='Login' component={LoginScreen} />
                    <Stack.Screen
                      name='TeacherScreen'
                      component={TeacherScreen}
                    />
                    <Stack.Screen
                      name='TeacherProfile'
                      component={TeacherProfile}
                    />
                    <Stack.Screen
                      name='TeacherTimetable'
                      component={TeacherTimetable}
                    />
                    <Stack.Screen
                      name='TeacherAttendance'
                      component={TeacherAttendanceScreen}
                    />
                    <Stack.Screen name='TeacherBPS' component={TeacherBPS} />
                    <Stack.Screen
                      name='TeacherHomework'
                      component={TeacherHomeworkScreen}
                    />
                    <Stack.Screen
                      name='TeacherHomeworkDetail'
                      component={TeacherHomeworkDetailScreen}
                    />
                    <Stack.Screen
                      name='TeacherHomeworkCreate'
                      component={TeacherHomeworkCreateScreen}
                    />
                    <Stack.Screen
                      name='TeacherMessagingScreen'
                      component={TeacherMessagingScreen}
                    />
                    <Stack.Screen
                      name='TeacherHealthScreen'
                      component={TeacherHealthScreen}
                    />
                    <Stack.Screen
                      name='HomeroomScreen'
                      component={HomeroomScreen}
                    />
                    <Stack.Screen
                      name='HomeroomStudents'
                      component={HomeroomStudentsScreen}
                    />
                    <Stack.Screen
                      name='HomeroomStudentProfile'
                      component={HomeroomStudentProfile}
                    />
                    <Stack.Screen
                      name='HomeroomAttendanceDetails'
                      component={HomeroomAttendanceDetailsScreen}
                    />
                    <Stack.Screen
                      name='HomeroomDiscipline'
                      component={HomeroomDisciplineScreen}
                    />
                    <Stack.Screen
                      name='ConversationScreen'
                      component={ConversationScreen}
                    />
                    <Stack.Screen
                      name='CreateConversation'
                      component={CreateConversationScreen}
                    />
                    <Stack.Screen
                      name='NotificationScreen'
                      component={NotificationScreen}
                    />
                    <Stack.Screen name='Settings' component={SettingsScreen} />
                    <Stack.Screen
                      name='WorkspaceScreen'
                      component={WorkspaceScreen}
                    />
                    <Stack.Screen
                      name='StaffReports'
                      component={StaffReportsScreen}
                    />
                    <Stack.Screen
                      name='ReportDetail'
                      component={ReportDetailScreen}
                    />
                  </Stack.Navigator>
                </NavigationContainer>
              </MessagingProvider>
            </NotificationProvider>
          </LanguageProvider>
        </ThemeProvider>
      </SafeAreaProvider>
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  // Add any app-specific styles here
});
