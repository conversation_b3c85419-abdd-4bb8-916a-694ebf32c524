import React, { useEffect, useRef, useState } from 'react';
import { StatusBar } from 'expo-status-bar';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { SafeAreaProvider } from 'react-native-safe-area-context';

// Import local components and contexts
import { ThemeProvider } from './src/contexts/ThemeContext';
import { LanguageProvider } from './src/contexts/LanguageContext';
import { NotificationProvider } from './src/contexts/NotificationContext';
import { MessagingProvider } from './src/contexts/MessagingContext';
import {
  requestUserPermission,
  notificationListener,
  setupLocalNotifications,
  setNavigationRef,
} from './src/utils/messaging';

// Import teacher-specific screens
import HomeScreen from './src/screens/HomeScreen';
import LoginScreen from './src/screens/LoginScreen';
import TeacherScreen from './src/screens/TeacherScreen';
import TeacherProfile from './src/screens/TeacherProfile';
import TeacherTimetable from './src/screens/TeacherTimetable';
import TeacherAttendanceScreen from './src/screens/TeacherAttendanceScreen';
import TeacherBPS from './src/screens/TeacherBPS';
import TeacherHomeworkScreen from './src/screens/TeacherHomeworkScreen';
import TeacherHomeworkDetailScreen from './src/screens/TeacherHomeworkDetailScreen';
import TeacherHomeworkCreateScreen from './src/screens/TeacherHomeworkCreateScreen';
import TeacherMessagingScreen from './src/screens/TeacherMessagingScreen';
import TeacherHealthScreen from './src/screens/TeacherHealthScreen';
import HomeroomScreen from './src/screens/HomeroomScreen';
import HomeroomStudentsScreen from './src/screens/HomeroomStudentsScreen';
import HomeroomStudentProfile from './src/screens/HomeroomStudentProfile';
import HomeroomAttendanceDetailsScreen from './src/screens/HomeroomAttendanceDetailsScreen';
import HomeroomDisciplineScreen from './src/screens/HomeroomDisciplineScreen';
import ConversationScreen from './src/screens/ConversationScreen';
import CreateConversationScreen from './src/screens/CreateConversationScreen';
import NotificationScreen from './src/screens/NotificationScreen';
import SettingsScreen from './src/screens/SettingsScreen';
import WorkspaceScreen from './src/screens/WorkspaceScreen';
import StaffReportsScreen from './src/screens/StaffReportsScreen';
import ReportDetailScreen from './src/screens/ReportDetailScreen';

const Stack = createNativeStackNavigator();

export default function App() {
  const navigationRef = useRef();
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Initialize messaging and notifications
        await requestUserPermission();
        await setupLocalNotifications();

        // Set up notification listeners
        const unsubscribe = notificationListener();

        setIsReady(true);

        return () => {
          if (unsubscribe) {
            unsubscribe();
          }
        };
      } catch (error) {
        console.error('Error initializing app:', error);
        setIsReady(true);
      }
    };

    initializeApp();
  }, []);

  if (!isReady) {
    return null; // Or a loading screen
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <SafeAreaProvider>
        <ThemeProvider>
          <LanguageProvider>
            <NotificationProvider>
              <MessagingProvider>
                <NavigationContainer
                  ref={navigationRef}
                  onReady={() => {
                    console.log(
                      '🧭 NAVIGATION: NavigationContainer is ready, setting reference...'
                    );
                    setTimeout(() => {
                      setNavigationRef(navigationRef.current);
                    }, 100);
                  }}
                >
                  <StatusBar style='auto' />
                  <Stack.Navigator
                    initialRouteName='Home'
                    screenOptions={{ headerShown: false }}
                  >
                    <Stack.Screen name='Home' component={HomeScreen} />
                    <Stack.Screen name='Login' component={LoginScreen} />
                    <Stack.Screen
                      name='TeacherScreen'
                      component={TeacherScreen}
                    />
                    <Stack.Screen
                      name='TeacherProfile'
                      component={TeacherProfile}
                    />
                    <Stack.Screen
                      name='TeacherTimetable'
                      component={TeacherTimetable}
                    />
                    <Stack.Screen
                      name='TeacherAttendance'
                      component={TeacherAttendanceScreen}
                    />
                    <Stack.Screen name='TeacherBPS' component={TeacherBPS} />
                    <Stack.Screen
                      name='TeacherHomework'
                      component={TeacherHomeworkScreen}
                    />
                    <Stack.Screen
                      name='TeacherHomeworkDetail'
                      component={TeacherHomeworkDetailScreen}
                    />
                    <Stack.Screen
                      name='TeacherHomeworkCreate'
                      component={TeacherHomeworkCreateScreen}
                    />
                    <Stack.Screen
                      name='TeacherMessagingScreen'
                      component={TeacherMessagingScreen}
                    />
                    <Stack.Screen
                      name='TeacherHealthScreen'
                      component={TeacherHealthScreen}
                    />
                    <Stack.Screen
                      name='HomeroomScreen'
                      component={HomeroomScreen}
                    />
                    <Stack.Screen
                      name='HomeroomStudents'
                      component={HomeroomStudentsScreen}
                    />
                    <Stack.Screen
                      name='HomeroomStudentProfile'
                      component={HomeroomStudentProfile}
                    />
                    <Stack.Screen
                      name='HomeroomAttendanceDetails'
                      component={HomeroomAttendanceDetailsScreen}
                    />
                    <Stack.Screen
                      name='HomeroomDiscipline'
                      component={HomeroomDisciplineScreen}
                    />
                    <Stack.Screen
                      name='ConversationScreen'
                      component={ConversationScreen}
                    />
                    <Stack.Screen
                      name='CreateConversation'
                      component={CreateConversationScreen}
                    />
                    <Stack.Screen
                      name='NotificationScreen'
                      component={NotificationScreen}
                    />
                    <Stack.Screen name='Settings' component={SettingsScreen} />
                    <Stack.Screen
                      name='WorkspaceScreen'
                      component={WorkspaceScreen}
                    />
                    <Stack.Screen
                      name='StaffReports'
                      component={StaffReportsScreen}
                    />
                    <Stack.Screen
                      name='ReportDetail'
                      component={ReportDetailScreen}
                    />
                  </Stack.Navigator>
                </NavigationContainer>
              </MessagingProvider>
            </NotificationProvider>
          </LanguageProvider>
        </ThemeProvider>
      </SafeAreaProvider>
    </GestureHandlerRootView>
  );
}
