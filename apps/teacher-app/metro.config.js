const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

const config = getDefaultConfig(__dirname);

// Add the shared library to the watchFolders
config.watchFolders = [
  path.resolve(__dirname, '../../shared'),
];

// Configure resolver to handle the shared library
config.resolver.nodeModulesPaths = [
  path.resolve(__dirname, 'node_modules'),
  path.resolve(__dirname, '../../node_modules'),
];

module.exports = config;
