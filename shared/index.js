// Export all shared components, contexts, services, and utilities

// Contexts
export {
  ThemeProvider,
  useTheme,
  getLanguageFontSizes,
} from './src/contexts/ThemeContext';
export {
  LanguageProvider,
  useLanguage,
  LANGUAGES,
} from './src/contexts/LanguageContext';
export {
  NotificationProvider,
  useNotifications,
} from './src/contexts/NotificationContext';
export {
  MessagingProvider,
  useMessaging,
} from './src/contexts/MessagingContext';

// Components
export * from './src/components';

// Services
export * from './src/services/authService';
export * from './src/services/logoutService';
export * from './src/services/demoModeService';
export * from './src/services/messagingService';
export * from './src/services/reportsService';
export * from './src/services/homeworkService';
export * from './src/services/schoolConfigService';
export * from './src/services/deviceService';

// Utils
export * from './src/utils/messaging';
export * from './src/utils/deviceDetection';
export * from './src/utils/commonStyles';
export * from './src/utils/orientationLock';
export * from './src/utils/htmlUtils';
export * from './src/utils/dataValidation';
export * from './src/utils/deviceInfo';

// Hooks
export * from './src/hooks/useScreenOrientation';
export * from './src/hooks/useParentNotifications';
export * from './src/hooks/useThemeLogo';

// Config
export * from './src/config/env';
