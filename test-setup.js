#!/usr/bin/env node

/**
 * Test script to validate the app separation setup
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing App Separation Setup...\n');

// Test 1: Check directory structure
console.log('1. Checking directory structure...');
const requiredDirs = [
  'apps/teacher-app',
  'apps/parent-student-app',
  'shared/src',
];

let structureValid = true;
requiredDirs.forEach(dir => {
  if (fs.existsSync(dir)) {
    console.log(`   ✅ ${dir} exists`);
  } else {
    console.log(`   ❌ ${dir} missing`);
    structureValid = false;
  }
});

// Test 2: Check package.json files
console.log('\n2. Checking package.json files...');
const packageFiles = [
  'package.json',
  'apps/teacher-app/package.json',
  'apps/parent-student-app/package.json',
  'shared/package.json',
];

let packagesValid = true;
packageFiles.forEach(file => {
  if (fs.existsSync(file)) {
    try {
      const pkg = JSON.parse(fs.readFileSync(file, 'utf8'));
      console.log(`   ✅ ${file} - ${pkg.name}`);
    } catch (e) {
      console.log(`   ❌ ${file} - Invalid JSON`);
      packagesValid = false;
    }
  } else {
    console.log(`   ❌ ${file} missing`);
    packagesValid = false;
  }
});

// Test 3: Check app configuration files
console.log('\n3. Checking app configuration files...');
const configFiles = [
  'apps/teacher-app/app.json',
  'apps/parent-student-app/app.json',
  'apps/teacher-app/babel.config.js',
  'apps/parent-student-app/babel.config.js',
];

let configValid = true;
configFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`   ✅ ${file} exists`);
  } else {
    console.log(`   ❌ ${file} missing`);
    configValid = false;
  }
});

// Test 4: Check shared library exports
console.log('\n4. Checking shared library exports...');
if (fs.existsSync('shared/index.js')) {
  const sharedIndex = fs.readFileSync('shared/index.js', 'utf8');
  const hasContexts = sharedIndex.includes('ThemeProvider') && sharedIndex.includes('LanguageProvider');
  const hasComponents = sharedIndex.includes('export * from \'./src/components\'');
  const hasServices = sharedIndex.includes('export * from \'./src/services/authService\'');
  
  if (hasContexts && hasComponents && hasServices) {
    console.log('   ✅ Shared library exports look good');
  } else {
    console.log('   ⚠️  Shared library exports may be incomplete');
  }
} else {
  console.log('   ❌ shared/index.js missing');
  configValid = false;
}

// Test 5: Check asset files
console.log('\n5. Checking asset files...');
const assetFiles = [
  'apps/teacher-app/assets/teacher_app_logo.png',
  'apps/parent-student-app/assets/parent_student_app_logo.png',
];

let assetsValid = true;
assetFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`   ✅ ${file} exists`);
  } else {
    console.log(`   ⚠️  ${file} missing (will need to be created)`);
  }
});

// Summary
console.log('\n📊 Summary:');
console.log(`   Directory Structure: ${structureValid ? '✅' : '❌'}`);
console.log(`   Package Files: ${packagesValid ? '✅' : '❌'}`);
console.log(`   Configuration: ${configValid ? '✅' : '❌'}`);
console.log(`   Assets: ${assetsValid ? '⚠️' : '❌'}`);

const overallValid = structureValid && packagesValid && configValid;
console.log(`\n🎯 Overall Setup: ${overallValid ? '✅ READY' : '❌ NEEDS WORK'}`);

if (overallValid) {
  console.log('\n🚀 Next steps:');
  console.log('   1. cd apps/teacher-app && npm install');
  console.log('   2. cd apps/parent-student-app && npm install');
  console.log('   3. cd shared && npm install');
  console.log('   4. Test each app: npm start');
} else {
  console.log('\n🔧 Please fix the issues above before proceeding.');
}

console.log('\n✨ App separation setup test complete!');
