# EduNova School Management System - Separated Apps

This project has been restructured to separate the Teacher and Parent/Student applications while maintaining shared functionality through a common library.

## Project Structure

```
edu-sis-paragon/
├── apps/
│   ├── teacher-app/           # Teacher-specific application
│   │   ├── App.js            # Teacher app entry point
│   │   ├── package.json      # Teacher app dependencies
│   │   ├── app.json          # Teacher app configuration
│   │   └── assets/           # Teacher app assets
│   └── parent-student-app/    # Parent/Student application
│       ├── App.js            # Parent/Student app entry point
│       ├── package.json      # Parent/Student app dependencies
│       ├── app.json          # Parent/Student app configuration
│       └── assets/           # Parent/Student app assets
├── shared/                    # Shared library
│   ├── src/                  # All shared source code
│   │   ├── components/       # Reusable components
│   │   ├── contexts/         # React contexts (Theme, Language, etc.)
│   │   ├── services/         # API services and business logic
│   │   ├── utils/            # Utility functions
│   │   ├── hooks/            # Custom React hooks
│   │   └── config/           # Configuration files
│   ├── package.json          # Shared library dependencies
│   └── index.js              # Shared library exports
└── package.json              # Workspace configuration
```

## Applications

### Teacher App
- **Bundle ID**: `com.edunovaasia.teacher`
- **Features**: 
  - Teacher dashboard and profile
  - Class timetable management
  - Attendance taking
  - BPS (Behavior Point System) management
  - Homework assignment and review
  - Homeroom management
  - Teacher messaging
  - Staff reports

### Parent/Student App
- **Bundle ID**: `com.edunovaasia.parentstudent`
- **Features**:
  - Parent dashboard with multiple student management
  - Student grades and assessments
  - Attendance tracking
  - Assignment viewing
  - Behavior point tracking
  - Student timetable
  - Calendar integration
  - Student messaging
  - Health records
  - Library access

## Development

### Prerequisites
- Node.js 18+
- Expo CLI
- React Native development environment

### Installation
```bash
# Install workspace dependencies
npm install

# Install teacher app dependencies
cd apps/teacher-app
npm install

# Install parent/student app dependencies
cd ../parent-student-app
npm install

# Install shared library dependencies
cd ../../shared
npm install
```

### Running the Apps

#### Teacher App
```bash
cd apps/teacher-app
npm start
```

#### Parent/Student App
```bash
cd apps/parent-student-app
npm start
```

### Building for Production

#### Teacher App
```bash
cd apps/teacher-app
npm run build:prod
```

#### Parent/Student App
```bash
cd apps/parent-student-app
npm run build:prod
```

## Shared Library

The shared library contains all common functionality:
- **Contexts**: Theme, Language, Notifications, Messaging
- **Components**: UI components, forms, charts, etc.
- **Services**: Authentication, API calls, data management
- **Utils**: Device detection, styling helpers, validation
- **Hooks**: Custom React hooks for common functionality

## Configuration

Each app has its own:
- Firebase configuration
- App icons and splash screens
- Bundle identifiers
- EAS project IDs

The shared library handles:
- API endpoints
- Theme configuration
- Language translations
- Common utilities

## Migration Notes

- All screens remain in the shared library for reusability
- Navigation is configured separately for each app
- Authentication services work with both apps
- Language context contains translations for both app types
- Asset management is handled per app for branding

## Next Steps

1. Test both apps independently
2. Update Firebase projects for separate apps
3. Configure separate EAS projects
4. Update CI/CD pipelines
5. Create separate app store listings
